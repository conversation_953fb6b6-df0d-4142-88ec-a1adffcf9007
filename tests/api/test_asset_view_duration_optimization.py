from datetime import timed<PERSON><PERSON>
from unittest.mock import patch
import factory

from django.test import TestCase, override_settings
from django.utils import timezone
from django.db.models import signals
from django.db import connection
from django.test.utils import override_settings
from rest_framework.test import APIClient

from app.domain.live_stream import get_streamed_duration
from app.models import LiveStream, LiveStreamEvent, Asset, AuthToken
from tests.factories import LiveStreamEventFactory, UserFactory
from tests.mixins import OrganizationMixin, AssetMixin


@override_settings(DEBUG=False, DEBUG_TOOLBAR_CONFIG={'SHOW_TOOLBAR_CALLBACK': lambda request: False})
@patch('app.domain.cloudfront.create_cdn', return_value=('test-cdn-id', 'https://test-cdn.example.com'))
@patch('app.domain.cloud_storage.create_bucket', return_value=None)
@patch('app.domain.cloudfront.create_key_group', return_value='test-key-group-id')
class AssetViewDurationOptimizationTest(OrganizationMixin, AssetMixin, TestCase):
    @factory.django.mute_signals(signals.post_save)
    def setUp(self, *mocks):
        self.organization = self.create_organization()
        self.user = UserFactory(organization=self.organization)
        self.asset = self.create_asset(organization=self.organization)
        self.live_stream = self.create_livestream(
            asset=self.asset,
            rtmp_url="rtmp://test.com/live",
            stream_key="test-stream-key",
            hls_url_path="/live/test-stream-key/index.m3u8"
        )
        
        # Set up API client with authentication
        self.client = APIClient()
        self.token = AuthToken.objects.create(user=self.user, organization=self.organization)[1]
        self.client.credentials(HTTP_AUTHORIZATION=f'Token {self.token}')

    def test_asset_retrieve_view_duration_optimization(self, *mocks):
        """Test that AssetUpdateDeleteRetrieveView uses O(1) duration calculation"""
        now = timezone.now()

        # Create multiple publish/unpublish cycles to test with realistic data
        events_data = [
            (LiveStreamEvent.Type.ON_PUBISH, now - timedelta(minutes=30)),
            (LiveStreamEvent.Type.ON_PUBISH_DONE, now - timedelta(minutes=25)),  # 5 min duration
            (LiveStreamEvent.Type.ON_PUBISH, now - timedelta(minutes=20)),
            (LiveStreamEvent.Type.ON_PUBISH_DONE, now - timedelta(minutes=15)),  # 5 min duration
            (LiveStreamEvent.Type.ON_PUBISH, now - timedelta(minutes=10)),
            (LiveStreamEvent.Type.ON_PUBISH_DONE, now - timedelta(minutes=5)),   # 5 min duration
        ]

        for event_type, created_time in events_data:
            LiveStreamEventFactory(
                organization=self.organization,
                live_stream=self.live_stream,
                type=event_type,
                created=created_time
            )

        # Test the serializer directly with the same prefetch as the view
        from django.db.models import Prefetch
        from app.api.v1.serializers.asset import AdminAssetDetailSerializer

        # Use the exact same prefetch as AssetUpdateDeleteRetrieveView
        asset_with_prefetch = Asset.objects.prefetch_related(
            Prefetch("live_stream__events", queryset=LiveStreamEvent.objects.order_by("created")),
            "video",
            "video__tracks",
            "video__inputs",
            "video__tracks__playlists",
            "live_stream"
        ).get(id=self.asset.id)

        # Test that the duration calculation specifically is optimized
        # (other parts of serialization may still trigger queries for related fields)
        live_stream = asset_with_prefetch.live_stream

        # The duration calculation should be O(1) due to prefetch
        with self.assertNumQueries(0):  # Should be 0 additional queries for duration
            duration = get_streamed_duration(live_stream)

        self.assertEqual(duration, 900)  # 15 minutes total (3 cycles of 5 minutes each)

    def test_get_streamed_duration_with_prefetched_events_from_asset_view(self, *mocks):
        """Test that get_streamed_duration is O(1) when using the same prefetch as AssetUpdateDeleteRetrieveView"""
        now = timezone.now()
        
        # Create events
        LiveStreamEventFactory(
            organization=self.organization,
            live_stream=self.live_stream,
            type=LiveStreamEvent.Type.ON_PUBISH,
            created=now - timedelta(minutes=10)
        )
        LiveStreamEventFactory(
            organization=self.organization,
            live_stream=self.live_stream,
            type=LiveStreamEvent.Type.ON_PUBISH_DONE,
            created=now - timedelta(minutes=5)
        )

        # Simulate the exact prefetch used in AssetUpdateDeleteRetrieveView
        from django.db.models import Prefetch
        
        asset_with_prefetch = Asset.objects.prefetch_related(
            Prefetch("live_stream__events", queryset=LiveStreamEvent.objects.order_by("created")),
            "live_stream"
        ).get(id=self.asset.id)

        # Test that get_streamed_duration is O(1) with this prefetch
        with self.assertNumQueries(0):  # Should be 0 additional queries
            duration = get_streamed_duration(asset_with_prefetch.live_stream)
        
        self.assertEqual(duration, 300)  # 5 minutes in seconds

    def test_get_streamed_duration_with_multiple_sessions_prefetched(self, *mocks):
        """Test O(1) performance with multiple streaming sessions"""
        now = timezone.now()
        
        # Create multiple streaming sessions
        events_data = [
            # Session 1: 10 minutes
            (LiveStreamEvent.Type.ON_PUBISH, now - timedelta(minutes=60)),
            (LiveStreamEvent.Type.ON_PUBISH_DONE, now - timedelta(minutes=50)),
            
            # Session 2: 15 minutes  
            (LiveStreamEvent.Type.ON_PUBISH, now - timedelta(minutes=40)),
            (LiveStreamEvent.Type.ON_PUBISH_DONE, now - timedelta(minutes=25)),
            
            # Session 3: 5 minutes
            (LiveStreamEvent.Type.ON_PUBISH, now - timedelta(minutes=20)),
            (LiveStreamEvent.Type.ON_PUBISH_DONE, now - timedelta(minutes=15)),
            
            # Some other event types that should be ignored
            (LiveStreamEvent.Type.CREATED, now - timedelta(minutes=65)),
            (LiveStreamEvent.Type.STOPPED, now - timedelta(minutes=10)),
        ]
        
        for event_type, created_time in events_data:
            LiveStreamEventFactory(
                organization=self.organization,
                live_stream=self.live_stream,
                type=event_type,
                created=created_time
            )

        # Use the same prefetch as AssetUpdateDeleteRetrieveView
        from django.db.models import Prefetch
        
        asset_with_prefetch = Asset.objects.prefetch_related(
            Prefetch("live_stream__events", queryset=LiveStreamEvent.objects.order_by("created")),
            "live_stream"
        ).get(id=self.asset.id)

        # Test O(1) performance even with many events
        with self.assertNumQueries(0):
            duration = get_streamed_duration(asset_with_prefetch.live_stream)
        
        # Total: 10 + 15 + 5 = 30 minutes = 1800 seconds
        self.assertEqual(duration, 1800)

    def test_get_streamed_duration_fallback_without_prefetch(self, *mocks):
        """Test that function still works without prefetch (fallback mode)"""
        now = timezone.now()
        
        # Create events
        LiveStreamEventFactory(
            organization=self.organization,
            live_stream=self.live_stream,
            type=LiveStreamEvent.Type.ON_PUBISH,
            created=now - timedelta(minutes=8)
        )
        LiveStreamEventFactory(
            organization=self.organization,
            live_stream=self.live_stream,
            type=LiveStreamEvent.Type.ON_PUBISH_DONE,
            created=now - timedelta(minutes=3)
        )

        # Get live stream without prefetch
        live_stream_no_prefetch = LiveStream.objects.get(id=self.live_stream.id)
        
        # This should still work but will trigger database queries
        duration = get_streamed_duration(live_stream_no_prefetch)
        
        self.assertEqual(duration, 300)  # 5 minutes in seconds

    def test_optimization_works_with_different_prefetch_patterns(self, *mocks):
        """Test that optimization works with different prefetch patterns"""
        now = timezone.now()

        # Create events for the live stream
        LiveStreamEventFactory(
            organization=self.organization,
            live_stream=self.live_stream,
            type=LiveStreamEvent.Type.ON_PUBISH,
            created=now - timedelta(minutes=12)
        )
        LiveStreamEventFactory(
            organization=self.organization,
            live_stream=self.live_stream,
            type=LiveStreamEvent.Type.ON_PUBISH_DONE,
            created=now - timedelta(minutes=7)
        )

        # Test with a different prefetch pattern (like what might be used in list views)
        from django.db.models import Prefetch

        asset_with_different_prefetch = Asset.objects.prefetch_related(
            "live_stream",
            Prefetch("live_stream__events", queryset=LiveStreamEvent.objects.all()),
        ).get(id=self.asset.id)

        # Should still be O(1) with this prefetch pattern
        with self.assertNumQueries(0):
            duration = get_streamed_duration(asset_with_different_prefetch.live_stream)

        self.assertEqual(duration, 300)  # 5 minutes
